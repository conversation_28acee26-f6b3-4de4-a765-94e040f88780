import http from './index'

const systemUrl = '/business-rent-admin'

/**
 * 账单类型枚举
 */
export enum CostType {
    /** 保证金 */
    DEPOSIT = 1,
    /** 租金 */
    RENT = 2,
    /** 其他费用 */
    OTHER = 3,
}

// 催缴查询DTO
export interface ContractBillQueryDTO {
    pageNum?: number
    pageSize?: number
    projectId?: string
    type?: number // 催缴类型(1催缴函 2催缴通知单)
    collectFlag?: number // 缴费状态(0未缴 1部分缴 2已缴)
    viewStatus?: number // 查看状态(0未查看 1已查看)
    contractNo?: string // 合同编号
    customerName?: string // 承租方
    sendTimeStart?: string // 发送日期起始时间
    sendTimeEnd?: string // 发送日期结束时间
}

// 催缴新增/修改DTO
export interface ContractBillAddDTO {
    id?: string
    projectId?: string
    projectName?: string
    customerId?: string
    customerName?: string
    contractId?: string
    contractNo?: string
    roomName?: string
    type?: number // 催缴类型(1催缴函 2催缴通知单)
    receivableDate?: string // 应收日期
    totalMoney?: number // 催缴金额
    lastReceivedMoney?: number // 账单生成时，除保证金外的已收金额
    collectFlag?: number // 缴费状态(0未缴 1部分缴 2已缴 3缴费中)
    collectTime?: string // 收齐时间
    sendTime?: string // 发送时间
    receivePerson?: string // 接收人
    receivePhone?: string // 接收人手机号
    viewStatus?: number // 查看状态(0未查看 1已查看)
    viewTime?: string // 查看时间
    previewUrl?: string // 预览地址
    isDel?: boolean
}

// 催缴主表信息VO
export interface ContractBillVo {
    id?: string
    projectId?: string
    projectName?: string
    customerId?: string
    customerName?: string
    contractId?: string
    contractNo?: string
    roomCount?: number // 租赁房源数
    roomName?: string // 房间名称，多个按照逗号分开
    type?: number // 催缴类型(1催缴函 2催缴通知单)
    startDate?: string // 合同开始日期
    endDate?: string // 合同结束日期
    receivableDate?: string // 应收日期
    totalMoney?: number // 催缴金额
    lastReceivedMoney?: number // 账单生成时，除保证金外的已收金额
    collectFlag?: number // 缴费状态(0未缴 1部分缴 2已缴 3缴费中)
    collectTime?: string // 收齐时间
    sendTime?: string // 发送时间
    receivePerson?: string // 接收人
    receivePhone?: string // 接收人手机号
    viewStatus?: number // 查看状态(0未查看 1已查看)
    viewTime?: string // 查看时间
    previewUrl?: string // 预览地址
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
}

// 催缴金额列表VO
export interface ContractBillMoneyVo {
    id?: string
    billId?: string // 催收id
    costType?: number // 账单类型
    startDate?: string // 开始日期
    endDate?: string // 结束日期
    receivableDate?: string // 应收日期
    totalAmount?: number // 账单总额
    discountAmount?: number // 优惠金额
    actualReceivable?: number // 实际应收金额
    receivedAmount?: number // 已收金额
    unreceivedAmount?: number // 未收金额
    createByName?: string
    updateByName?: string
    isDel?: boolean
}

// 催缴配置DTO
export interface ContractBillConfigAddDTO {
    id?: string
    status?: boolean // 开启状态(0-否,1-是)
    type?: number // 类型(1营收抽点 2固定金额)
    propertyType?: number // 业态（0不限 1宿舍 2非宿舍）
    customerType?: number // 客户类型（0不限 1个人 2企业）
    payPeriod?: number // 支付周期（0不限 1月付 2年付）
    ruleConfig?: string // 固定金额规则配置
    isDel?: boolean
}

// 催缴详情VO（扁平化结构）
export interface ContractBillDetailVo {
    id?: string
    projectId?: string
    projectName?: string
    customerId?: string
    customerName?: string
    contractId?: string
    contractNo?: string
    roomCount?: number // 租赁房源数
    roomName?: string // 房间名称，多个按照逗号分开
    type?: number // 催缴类型(1催缴函 2催缴通知单)
    startDate?: string // 合同开始日期
    endDate?: string // 合同结束日期
    receivableDate?: string // 应收日期
    totalMoney?: number // 催缴金额
    receivedAmount?: number // 当前已缴金额
    currentUnreceivedMoney?: number // 当前未缴金额
    collectFlag?: number // 缴费状态(0未缴 1部分缴 2已缴 3缴费中)
    collectTime?: string // 收齐时间
    sendTime?: string // 发送时间
    receivePerson?: string // 接收人
    receivePhone?: string // 接收人手机号
    viewStatus?: number // 查看状态(0未查看 1已查看)
    viewTime?: string // 查看时间
    previewUrl?: string // 预览地址
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    moneyList?: ContractBillMoneyVo[] // 催缴金额列表
}

// 表格数据信息
export interface TableDataInfo {
    total?: number
    rows?: any[]
    code?: number
    msg?: string
}

// 通用返回结果
export interface AjaxResult {
    error?: boolean
    success?: boolean
    warn?: boolean
    empty?: boolean
    [key: string]: any
}

/**
 * 1. 查询催缴列表
 * @param params 查询参数
 * @returns Promise<TableDataInfo>
 */
export function getContractBillList(params: ContractBillQueryDTO) {
    return http.get<TableDataInfo>(`${systemUrl}/contract/bill/list`, params)
}

/**
 * 2. 获取催缴详细信息
 * @param id 催缴ID
 * @returns Promise<ContractBillDetailVo>
 */
export function getContractBillDetail(id: string) {
    return http.get<ContractBillDetailVo>(`${systemUrl}/contract/bill/detail`, { id })
}

/**
 * 3. 下载催缴函
 * @param id 催缴ID
 * @returns Promise<{msg: string, code: number}>
 */
export function downloadContractBill(id: string) {
    return http.get(`${systemUrl}/contract/bill/preview`, { id })
}

/**
 * 4. 导出催缴列表
 * @param params 查询参数
 * @returns Promise<any>
 */
export function exportContractBillList(params: ContractBillQueryDTO) {
    return http.post(`${systemUrl}/contract/bill/export`, params, {
        responseType: 'blob'
    })
}

/**
 * 5. 新增催缴
 * @param data 催缴数据
 * @returns Promise<AjaxResult>
 */
export function addContractBill(data: ContractBillAddDTO) {
    return http.post<AjaxResult>(`${systemUrl}/contract/bill`, data)
}

/**
 * 6. 修改催缴
 * @param data 催缴数据
 * @returns Promise<AjaxResult>
 */
export function updateContractBill(data: ContractBillAddDTO) {
    return http.put<AjaxResult>(`${systemUrl}/contract/bill`, data)
}

/**
 * 7. 删除催缴
 * @param ids 催缴ID列表
 * @returns Promise<AjaxResult>
 */
export function deleteContractBill(ids: string[]) {
    return http.delete<AjaxResult>(`${systemUrl}/contract/bill/delete`, { ids })
}

/**
 * 8. 手动生成催缴通知单和催缴函
 * @param contractId 合同ID
 * @returns Promise<AjaxResult>
 */
export function generateContractBill(contractId: string) {
    return http.post<AjaxResult>(`${systemUrl}/contract/bill/generate?contractId=${contractId}`)
}

/**
 * 9. 定时任务生成催缴通知单和催缴函
 * @returns Promise<AjaxResult>
 */
export function cronJobGenContractBill() {
    return http.post<AjaxResult>(`${systemUrl}/contract/bill/cronJobGen`)
}

/**
 * 10. 获取催缴配置详细信息
 * @returns Promise<AjaxResult>
 */
export function getContractBillConfig() {
    return http.get<AjaxResult>(`${systemUrl}/contract/bill/config/detail`)
}

/**
 * 11. 保存催缴配置
 * @param data 催缴配置数据
 * @returns Promise<AjaxResult>
 */
export function saveContractBillConfig(data: ContractBillConfigAddDTO[]) {
    return http.post<AjaxResult>(`${systemUrl}/contract/bill/config/save`, data)
}
