<template>
    <div class="contract-entry-exit">
        <section>
            <sectionTitle title="进场信息"></sectionTitle>
        </section>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table :columns="columns" :data="tableData" :pagination="entryPagination" :scroll="{ x: 1100 }"
                :bordered="{ cell: true }" @page-change="handleEntryPageChange">
                <template #operations="{ record }">
                    <a-button v-if="!!record.id" type="text" size="mini" @click="handleEntryDetail(record)">
                        详情
                    </a-button>
                </template>
            </a-table>
        </a-card>
        <section>
            <sectionTitle title="出场信息"></sectionTitle>
        </section>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table :columns="columns2" :data="tableData2" :pagination="exitPagination" :scroll="{ x: 1200 }"
                :bordered="{ cell: true }" @page-change="handleExitPageChange">
                <template #terminateType="{ record }">
                    {{ getTerminateTypeText(record.terminateType) }}
                </template>
                <template #progressStatus="{ record }">
                    <a-tag :color="getProgressStatusColor(record.progressStatus)">
                        {{ getProgressStatusText(record.progressStatus) }}
                    </a-tag>
                </template>
                <template #processType="{ record }">
                    <a-tag :color="getProcessTypeColor(record.processType)">
                        {{ getProcessTypeText(record.processType) }}
                    </a-tag>
                </template>
                <template #signType="{ record }">
                    <a-tag :color="getSignTypeColor(record.signType)">
                        {{ getSignTypeText(record.signType) }}
                    </a-tag>
                </template>
                <template #operations="{ record }">
                    <a-button type="text" size="mini" @click="handleExitDetail(record)">
                        详情
                    </a-button>
                </template>
            </a-table>
        </a-card>

        <!-- 进场详情抽屉 -->
        <a-drawer v-model:visible="entryDetailVisible" title="进场详情" width="1200px" :footer="false">
            <entry-detail v-if="entryDetailVisible" :data="currentEntryData || {}" :mode="'view'" />
        </a-drawer>

        <!-- 出场详情抽屉 -->
        <a-drawer v-model:visible="exitDetailVisible" title="出场详情" width="1200px" :footer="false">
            <exit-handler v-if="exitDetailVisible" :data="currentExitData || {}" :mode="'view'" />
        </a-drawer>
    </div>
</template>

<script setup lang="ts">
import { watch, onMounted } from 'vue';
import sectionTitle from '@/components/sectionTitle/index.vue';
import { getDictLabel } from '@/dict';
import { useContractStore } from '@/store/modules/contract/index';
import { getEnterList, getEnterDetailWithRooms } from '@/api/entry';
import { getExitList, getExitDetail } from '@/api/operationManage';
import EntryDetail from '@/views/operationManage/components/entryDetail.vue';
import ExitHandler from '@/views/operationManage/components/exitHandlerNew.vue';

const contractStore = useContractStore();
const contractData = computed(() => contractStore.contractDetail as any);

const tableData = ref<any[]>([]);

// 进场数据分页
const entryPagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100],
});

// 出场数据分页
const exitPagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100],
});

const columns = computed(() => {
    return [
        {
            title: '序号',
            dataIndex: 'index',
            width: 70,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '合同编号',
            dataIndex: 'contractNo',
            width: 160,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '合同用途',
            dataIndex: 'contractPurpose',
            width: 100,
            align: 'center',
            ellipsis: true,
            tooltip: true,
            render: ({ record }: { record: any }) => {
                return getDictLabel(
                    'diversification_purpose',
                    record.contractPurpose?.toString()
                );
            },
        },
        {
            title: '承租方',
            dataIndex: 'tenantName',
            width: 140,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '租期',
            dataIndex: 'entryTime',
            width: 180,
            align: 'center',
            ellipsis: true,
            tooltip: true,
            render: ({ record }: { record: any }) => {
                return record.rentStartDate && record.rentEndDate
                    ? `${record.rentStartDate} 至 ${record.rentEndDate}`
                    : '';
            },
        },
        {
            title: '未进场房源',
            dataIndex: 'roomName',
            width: 220,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '未进场房源数',
            dataIndex: 'unenterNum',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '已进场房源',
            dataIndex: 'roomName',
            width: 220,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '已进场房源数',
            dataIndex: 'enteredRoomCount',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '操作',
            slotName: 'operations',
            width: 120,
            fixed: 'right',
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
    ];
});
const tableData2 = ref<any[]>([]);

const columns2 = computed(() => {
    return [
        {
            title: '序号',
            dataIndex: 'index',
            width: 70,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '合同编号',
            dataIndex: 'contractNo',
            width: 160,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '合同用途',
            dataIndex: 'contractPurpose',
            width: 100,
            align: 'center',
            ellipsis: true,
            tooltip: true,
            render: ({ record }: { record: any }) => {
                return getDictLabel(
                    'diversification_purpose',
                    record.contractPurpose?.toString()
                );
            },
        },
        {
            title: '承租方',
            dataIndex: 'customerName',
            width: 140,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '退租类型',
            dataIndex: 'terminateType',
            slotName: 'terminateType',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '退租日期',
            dataIndex: 'terminateDate',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '退租房源',
            dataIndex: 'terminateRoomName',
            width: 200,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '房源数',
            dataIndex: 'terminateRoomCount',
            width: 80,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '办理状态',
            dataIndex: 'status',
            slotName: 'status',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
            render: ({ record }: { record: any }) => {
                return getStatusText(record.status);
            },
        },
        {
            title: '办理进度',
            dataIndex: 'progressStatus',
            slotName: 'progressStatus',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '费用合计',
            dataIndex: 'finalAmount',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '办理流程',
            dataIndex: 'processType',
            slotName: 'processType',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '签字方式',
            dataIndex: 'signType',
            slotName: 'signType',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '操作',
            slotName: 'operations',
            width: 120,
            fixed: 'right',
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
    ];
});

// 加载进场数据
const loadEntryData = async (page = 1, pageSize = 10) => {
    try {
        // 检查contractData是否有值
        if (!contractData.value || !contractData.value.unionId) {
            console.log('contractData或unionId为空，跳过加载进场数据');
            return;
        }

        const params = {
            contractUnionId: contractData.value.unionId, // 使用合同的unionId
            pageNum: page,
            pageSize: pageSize,
            // 不传type，取所有数据
        };
        const response = await getEnterList(params);
        if (response && response.rows) {
            // 为数据添加序号
            tableData.value = (response.rows || []).map((item: any, index: number) => ({
                ...item,
                index: (page - 1) * pageSize + index + 1
            }));
            // 更新分页信息
            entryPagination.value.total = response.total || 0;
            entryPagination.value.current = page;
            entryPagination.value.pageSize = pageSize;
        }
    } catch (error) {
        console.error('获取进场列表失败:', error);
        tableData.value = [];
    }
};

// 加载出场数据
const loadExitData = async (page = 1, pageSize = 10) => {
    try {
        // 检查contractData是否有值
        if (!contractData.value || !contractData.value.unionId) {
            console.log('contractData或unionId为空，跳过加载出场数据');
            return;
        }

        const params = {
            contractUnionId: contractData.value.unionId, // 使用合同的unionId
            pageNum: page,
            pageSize: pageSize,
            // 不传status，取所有数据
        };
        const response = await getExitList(params);
        if (response && response.rows) {
            // 为数据添加序号
            tableData2.value = (response.rows || []).map((item: any, index: number) => ({
                ...item,
                index: (page - 1) * pageSize + index + 1
            }));
            // 更新分页信息
            exitPagination.value.total = response.total || 0;
            exitPagination.value.current = page;
            exitPagination.value.pageSize = pageSize;
        }
    } catch (error) {
        console.error('获取出场列表失败:', error);
        tableData2.value = [];
    }
};

// 详情弹窗控制
const entryDetailVisible = ref(false);
const exitDetailVisible = ref(false);
const currentEntryData = ref<any>(null);
const currentExitData = ref<any>(null);

// 查看进场详情
const handleEntryDetail = async (record: any) => {
    try {
        const response = await getEnterDetailWithRooms(record.id);
        if (response && response.data) {
            currentEntryData.value = {
                ...record,
                ...response.data
            };
            entryDetailVisible.value = true;
        }
    } catch (error) {
        console.error('获取进场详情失败:', error);
    }
};

// 查看出场详情
const handleExitDetail = async (record: any) => {
    try {
        const response = await getExitDetail(record.id);
        if (response && response.data) {
            currentExitData.value = {
                ...record,
                ...response.data
            };
            exitDetailVisible.value = true;
        }
    } catch (error) {
        console.error('获取出场详情失败:', error);
    }
};

// 获取退租类型文本
const getTerminateTypeText = (type?: number) => {
    switch (type) {
        case 0:
            return '到期退租';
        case 1:
            return '提前退租';
        default:
            return '未知';
    }
};
// 获取状态文本
const getStatusText = (status?: number) => {
    switch (status) {
        case 0:
            return '待办理';
        case 1:
            return '办理中';
        case 2:
            return '已办理';
        default:
            return '未知';
    }
};
// 获取办理进度文本
const getProgressStatusText = (status?: number) => {
    // 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废
    switch (status) {
        case 0:
            return '不可见';
        case 1:
            return '待办理';
        case 5:
            return '物业交割';
        case 10:
            return '费用结算';
        case 15:
            return '交割并结算';
        case 20:
            return '客户签字';
        case 25:
            return '发起退款';
        case 30:
            return '已完成';
        case 40:
            return '已作废';
        default:
            return '未知';
    }
};

// 获取办理进度状态颜色
const getProgressStatusColor = (status?: number) => {
    switch (status) {
        case 0:
            return 'gray';
        case 1:
            return 'blue';
        case 5:
            return 'blue';
        case 10:
            return 'green';
        case 15:
            return 'orange';
        case 20:
            return 'green';
        case 25:
            return 'red';
        case 30:
            return 'blue';
        case 40:
            return 'red';
        default:
            return 'default';
    }
};

// 获取办理流程文本
const getProcessTypeText = (processType?: number) => {
    switch (processType) {
        case 1:
            return '先交割后结算';
        case 2:
            return '交割并结算';
        default:
            return '未知';
    }
};

// 获取办理流程颜色
const getProcessTypeColor = (processType?: number) => {
    switch (processType) {
        case 1:
            return 'blue';
        case 2:
            return 'green';
        default:
            return 'default';
    }
};

// 获取签字方式文本
const getSignTypeText = (signType?: number) => {
    switch (signType) {
        case 1:
            return '线上签字';
        case 2:
            return '线下签字';
        default:
            return '未设置';
    }
};

// 获取签字方式颜色
const getSignTypeColor = (signType?: number) => {
    switch (signType) {
        case 1:
            return 'blue';
        case 2:
            return 'orange';
        default:
            return 'gray';
    }
};

// 进场数据分页变化处理
const handleEntryPageChange = (page: number, pageSize: number) => {
    loadEntryData(page, pageSize);
};

// 出场数据分页变化处理
const handleExitPageChange = (page: number, pageSize: number) => {
    loadExitData(page, pageSize);
};

// 组件初始化时加载数据
onMounted(() => {
    // 如果contractData已经有值，直接加载数据
    if (contractData.value && contractData.value.unionId) {
        loadEntryData(1, entryPagination.value.pageSize);
        loadExitData(1, exitPagination.value.pageSize);
    }
});

// 监听contractData的变化，当有值时加载数据
watch(
    () => contractData.value?.unionId,
    (newUnionId) => {
        if (newUnionId) {
            loadEntryData(1, entryPagination.value.pageSize);
            loadExitData(1, exitPagination.value.pageSize);
        }
    },
    { immediate: true }
);
</script>

<style scoped lang="less">
.info-section {
    margin-bottom: 24px;
}

.info-item {
    display: flex;
    margin-bottom: 12px;

    .label {
        font-weight: 500;
        color: var(--color-text-2);
        min-width: 80px;
        margin-right: 8px;
    }

    .value {
        color: var(--color-text-1);
        flex: 1;
    }
}
</style>
