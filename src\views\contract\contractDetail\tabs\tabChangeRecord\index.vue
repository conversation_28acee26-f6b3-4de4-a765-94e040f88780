<template>
    <div class="contract-change-record">
        <section>
            <sectionTitle title="修改记录"></sectionTitle>
        </section>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table
                :columns="columns"
                :data="tableData"
                :pagination="false"
                :scroll="{ x: 1000 }"
                :bordered="{ cell: true }"
                :loading="loading"
                :empty="emptyText"
            ></a-table>
        </a-card>
    </div>
</template>

<script setup lang="ts">
    import sectionTitle from '@/components/sectionTitle/index.vue';
    import { getModifyLog, ContractModifyLogVo } from '@/api/contract';
    import { useContractStore } from '@/store/modules/contract/index';
    import dayjs from 'dayjs';

    const contractStore = useContractStore();
    const contractData = computed(() => contractStore.contractDetail as any);
    
    // 扩展修改记录类型，包含时间字段
    interface ExtendedModifyLogVo extends ContractModifyLogVo {
        createTime?: string;
        updateTime?: string;
    }
    
    const tableData = ref<ExtendedModifyLogVo[]>([]);
    const loading = ref(false);

    // 空数据文本
    const emptyText = computed(() => {
        if (loading.value) return '';
        return '暂无修改记录';
    });

    // 修改类型映射
    const modifyTypeMap = new Map([
        [0, '付款账号'],
        [1, '更新联系人'],
        [2, '承租人手机号'],
        [3, '签约方式']
    ]);

    const getModifyTypeText = (modifyType: number) => {
        return modifyTypeMap.get(modifyType) || '未知类型';
    };

    const columns = computed(() => {
        return [
            {
                title: '修改类型',
                dataIndex: 'modifyType',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
                render: ({ record }: { record: ExtendedModifyLogVo }) => {
                    return getModifyTypeText(record.modifyType);
                }
            },
            {
                title: '修改日期',
                dataIndex: 'createTime',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
                render: ({ record }: { record: ExtendedModifyLogVo }) => {
                    return record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-';
                }
            },
            {
                title: '修改人',
                dataIndex: 'updateByName',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '修改前',
                dataIndex: 'oldValue',
                width: 215,
                align: 'center',
                ellipsis: true,
                tooltip: true,
                render: ({ record }: { record: ExtendedModifyLogVo }) => {
                    if (record.modifyType === 3) {
                        // 由于 oldValue 可能为字符串类型，这里用字符串比较
                        return getValueText(record.oldValue);
                    }
                    return record.oldValue || '-';
                }
            },
            {
                title: '修改后',
                dataIndex: 'newValue',
                width: 215,
                align: 'center',
                ellipsis: true,
                tooltip: true,
                render: ({ record }: { record: ExtendedModifyLogVo }) => {
                    if (record.modifyType === 3) {
                        // 由于 newValue 可能为字符串类型，这里用字符串比较
                        return getValueText(record.newValue);
                    }
                    return record.newValue || '-';
                }
            },
        ];
    });
    
    const getValueText = (oldValue: string) => {
        if (oldValue === '0') {
            return '电子合同';
        }else if (oldValue === '1') {
            return '纸质合同（甲方电子章）';
        }else if (oldValue === '2') {
            return '纸质合同（双方实体章）';
        }
    }

    // 获取修改记录
    const getModifyLogList = async () => {
        if (!contractData.value?.id) {
            tableData.value = [];
            return;
        }
        
        loading.value = true;
        try {
            const response = await getModifyLog(contractData.value.id);
            if (response && response.data) {
                tableData.value = response.data;
            } else {
                tableData.value = [];
            }
        } catch (error) {
            console.error('获取修改记录失败:', error);
            tableData.value = [];
        } finally {
            loading.value = false;
        }
    };

    // 监听合同数据变化
    watch(() => contractData.value?.id, (newVal) => {
        if (newVal) {
            getModifyLogList();
        } else {
            tableData.value = [];
        }
    }, { immediate: true });
</script>

<style scoped lang="less"></style>
