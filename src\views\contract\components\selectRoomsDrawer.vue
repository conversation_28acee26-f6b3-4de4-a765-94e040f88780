<template>
    <a-drawer class="common-drawer-small" :title="contractData.contractType === 2 ? '选择多经点位' : '选择房源'"
        :visible="visible" @ok="handleOk" @cancel="handleCancel" unmountOnClose>
        <a-space direction="vertical" fill>
            <section>
                <sectionTitle title="房源列表"></sectionTitle>
                <a-card :bordered="false" :body-style="{ padding: '16px 0 0' }">
                    <a-row align="stretch">
                        <a-col :flex="1">
                            <a-form :model="queryForm" auto-label-width>
                                <a-grid :cols="2" :col-gap="16">
                                    <a-grid-item>
                                        <a-form-item label="物业类型" field="buildingType">
                                            <a-tree-select v-model="queryForm.buildingType" :data="purposeTypeOptions"
                                                placeholder="请选择物业类型" allow-clear
                                                :fieldNames="{ title: 'label', key: 'value' }"></a-tree-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item label="楼栋" field="buildingId">
                                            <a-tree-select selectable="leaf" :data="buildingTree"
                                                :fieldNames="{ label: 'name', value: 'id' }"
                                                v-model="queryForm.buildingId" placeholder="请选择楼栋"
                                                allow-clear></a-tree-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item label="租控状态" field="rentStatus">
                                            <a-select v-model="queryForm.rentStatus" placeholder="请选择租控状态" allow-clear>
                                                <a-option value="0">可租</a-option>
                                                <a-option value="1">已租</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item label="房源名称" field="roomName">
                                            <a-input v-model="queryForm.roomName" placeholder="请输入房源名称" />
                                        </a-form-item>
                                    </a-grid-item>
                                    <!-- <a-grid-item>
                                        <a-button type="primary" @click="handleQuery" :loading="loading">查询</a-button>
                                    </a-grid-item> -->
                                </a-grid>
                            </a-form>
                        </a-col>
                        <a-divider style="margin-bottom: 16px;" direction="vertical" />
                        <a-col flex="86px">
                            <a-button type="primary" @click="handleQuery" :loading="loading">查询</a-button>
                            <a-button style="margin-top: 15px;" type="primary" @click="handleCheck">加入</a-button>
                        </a-col>
                    </a-row>
                </a-card>
                <a-card>
                    <a-tree ref="treeRef" blockNode checkable v-model:checked-keys="checkedKeys"
                        checked-strategy="child" :data="treeData" :loading="loading" :virtualListProps="{
                            height: 200,
                        }" />
                </a-card>
                <a-card :bordered="false" :body-style="{ display: 'flex', justifyContent: 'center' }">
                    <a-space>
                    </a-space>
                </a-card>
            </section>
            <section>
                <sectionTitle title="选中房源列表"></sectionTitle>
                <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
                    <a-space direction="vertical" fill>
                        <a-space class="table-query">
                            <a-input v-model="tableQuery" placeholder="请输入房源"></a-input>
                            <a-button type="primary" @click="handleTableQuery">查询</a-button>
                             <a-button class="remove-btn" type="primary" @click="handleRemove">移除</a-button>
                        </a-space>
                        <a-table :data="tableQueryData" rowKey="roomId" :row-selection="rowSelection"
                            v-model:selectedKeys="selectedKeys" :virtual-list-props="{ height: 200 }"
                            :pagination="false" :columns="tableColumns" @selection-change="handleSelectionChange">
                        </a-table>
                    </a-space>
                </a-card>
            </section>
        </a-space>
    </a-drawer>
</template>

<script lang="ts" setup>
import { ContractRoomDTO } from '@/api/contract';
import { getRoomTree, type RoomTreeQueryDTO, type RoomTreeVo } from '@/api/room';
import sectionTitle from '@/components/sectionTitle/index.vue';
import { cloneDeep } from 'lodash';
import { Message } from '@arco-design/web-vue';
import { useContractStore } from '@/store/modules/contract/index';
import { getDict, getDictLabel } from '@/dict';
import { useDict } from '@/utils/dict'

const { property_type } = useDict('property_type');


const contractStore = useContractStore()
const contractData = computed(() => contractStore.contractData)

const diversificationPurpose = getDict('diversification_purpose');
const purposeTypeOptions = computed(() => {
    if (contractData.value.contractType === 0) {
        return diversificationPurpose.filter((item: any) => ['厂房', '商业', '车位', '办公'].includes(item.label))
    } else if (contractData.value.contractType === 1) {
        return diversificationPurpose.filter((item: any) => ['宿舍'].includes(item.label))
    } else if (contractData.value.contractType === 2) {
        return diversificationPurpose.filter((item: any) => !['日租房'].includes(item.label))
    }
})

const visible = ref(false);
const loading = ref(false);

// 存储原有合同的房源ID列表，用于过滤
const originalContractRoomIds = ref<string[]>([]);

const openDrawer = (originRooms: any[] = [], rooms: any[] = []) => {
    // 保存原有合同的房源ID列表
    originalContractRoomIds.value = originRooms.map(item => item.roomId);
    
    // 过滤出新房源，只保留不在原有合同中的房源
    // 注意：这里需要保留已经选择的新房源，即使它们的roomId可能在原有合同中
    // 因为换房时，新房源可能是从原有合同中重新选择的
    const newRooms = rooms.filter(room => {
        // 如果是换房场景，需要保留已经选择的房源
        if (room.type === 1) {
            return true; // 保留所有新房源
        }
        // 否则过滤掉原有合同中的房源
        return !originalContractRoomIds.value.includes(room.roomId);
    });
    
    // 新房源选择传递的数据
    checkedKeys.value = newRooms.map(item => item.roomId);
    tableQuery.value = '';
    tableQueryData.value = JSON.parse(JSON.stringify(newRooms));
    selectedKeys.value = newRooms.map(item => item.roomId);
    tableData.value = JSON.parse(JSON.stringify(newRooms));
    visible.value = true;
    queryForm.value.contractType = contractData.value.contractType + ''
    // 打开抽屉时加载房源树数据
    loadRoomTree();
    
    console.log('openDrawer - 初始化完成，原有合同房源ID:', originalContractRoomIds.value);
    console.log('openDrawer - 过滤后的新房源:', newRooms);
}

// 房源列表
const queryForm = ref<RoomTreeQueryDTO>({
    buildingType: undefined,
    buildingId: undefined,
    rentStatus: undefined,
    roomName: undefined,
    projectId: contractStore.currentProjectId||contractData.value.projectId,
    contractType: '',
    pricingFlag: 1
})

// 房源树数据
const treeData = ref<any[]>([]);

// 转换房源树数据格式，并过滤掉原有合同的房源
const transformTreeData = (data: RoomTreeVo[]): any[] => {
    if (!Array.isArray(data)) {
        return [];
    }

    return data.map(item => {
        const transformedItem: any = {
            title: item.name || item.roomName || '未命名',
            key: item.id || item.roomId || Math.random().toString(36).substr(2, 9),
            ...item
        };

        // 递归处理子节点
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
            transformedItem.children = transformTreeData(item.children);
        }

        // 如果是房间级别的节点（有roomId），检查是否在原有合同房源中
        if (item.roomId && originalContractRoomIds.value.includes(item.roomId)) {
            // 在原有合同中的房源，设置为禁用状态
            transformedItem.disabled = true;
            // 添加特殊标识，在标题中显示"已租"
            transformedItem.title = `${transformedItem.title} (已租)`;
            // 设置不可选中
            transformedItem.selectable = false;
            console.log('transformTreeData - 禁用原有合同房源:', item.roomName);
        }

        return transformedItem;
    });
};

// 获取楼栋树, 递归遍历房源数，过滤掉房间节点
const buildingTree = ref<any[]>([]);
const filterTreeData = (data: any[]) => {
    return data.filter(item => {
        if (item.level <= 3) {
            if (item.children) {
                item.children = filterTreeData(item.children);
            }
            return true;
        }
        return false;
    });
};

// 加载房源树数据
const loadRoomTree = async () => {
    try {
        loading.value = true;
        const response = await getRoomTree(queryForm.value);
        console.log('loadRoomTree - API响应:', response);
        
        treeData.value = transformTreeData(response.data || []);
        console.log('loadRoomTree - 转换后的树数据:', treeData.value);
        
        if(!queryForm.value.buildingType && !queryForm.value.buildingId && !queryForm.value.roomName && !queryForm.value.rentStatus) {
            buildingTree.value = filterTreeData(JSON.parse(JSON.stringify(treeData.value)))
        }
        nextTick(() => {
            // 设置选中 - 选中新房源和已经选择的房源
            treeRef.value?.expandAll();
            // 选中新房源和已经选择的房源
            const newRoomIds = tableData.value
                .filter(item => item.type === 1 || !originalContractRoomIds.value.includes(item.roomId))
                .map(item => item.roomId);
            selectedKeys.value = newRoomIds;
            treeRef.value?.checkNode(selectedKeys.value, true);
        })
    } catch (error) {
        console.error('加载房源树失败:', error);
    } finally {
        loading.value = false;
    }
};

// 查询房源
const handleQuery = () => {
    loadRoomTree();
};
const checkedKeys = ref<string[]>([]);
const treeRef = useTemplateRef('treeRef');
const handleCheck = () => {
    const checkedNodes = treeRef.value?.getCheckedNodes();
    console.log('handleCheck - 选中的节点:', checkedNodes);
    
    // 从当前tableData开始，而不是从空数组开始
    const roomList: ContractRoomDTO[] = cloneDeep(tableData.value)
    
    let hasNewRooms = false;
    let newRoomsToAdd: ContractRoomDTO[] = [];
    
    checkedNodes.forEach((item: any) => {
        // 过滤掉原有合同的房源，但保留已经选择的新房源
        if (originalContractRoomIds.value.includes(item.roomId) && !tableData.value.some(room => room.roomId === item.roomId && room.type === 1)) {
            console.log('handleCheck - 过滤掉原有合同房源:', item.roomName);
            return;
        }
        
        // 检查是否已经在列表中
        if(roomList.every(room => room.roomId !== item.roomId)) {
            console.log('handleCheck - 准备添加新房源:', item.roomName);
            hasNewRooms = true;
            const newRoom = {
                id: item.id,
                contractId: undefined,
                roomId: item.roomId,
                roomName: item.roomName,
                area: item.rentArea || 0,
                standardUnitPrice: item.price,
                bottomPrice: item.bottomPrice || 0,
                priceUnit: item.priceUnit || 0,
                discount: 100,
                signedUnitPrice: 0,
                signedMonthlyPrice: 0,
                startDate: item.startDate || '',
                endDate: item.endDate || '',
                propertyType: item.propertyType || '',
                bondPriceType: item.depositType,
                bondPrice: item.depositAmount,
                buildingName: item.buildingName,
                floorName: item.floorName,
                parcelName: item.parcelName,
                stageName: item.stageName,
            };
            newRoomsToAdd.push(newRoom);
        } else {
            console.log('handleCheck - 房源已存在:', item.roomName);
        }
    })
    
    if (hasNewRooms) {
        // 在选择房源时进行校验
        const allRooms = [...roomList, ...newRoomsToAdd];
        const newRoomsOnly = allRooms.filter(item => item.type === 1 || !originalContractRoomIds.value.includes(item.roomId));
        
        if (newRoomsOnly.length > 1) {
            // 判断选择的房源计价方式和物业类型是否一样
            const priceUnit = newRoomsOnly[0].priceUnit;
            const isSamePriceUnit = newRoomsOnly.every(item => String(item.priceUnit) === String(priceUnit));
            if (!isSamePriceUnit) {
                Message.warning('该房源和已选择房源计价方式不同，请重新选择');
                return;
            }
            
            const propertyType = newRoomsOnly[0].propertyType;
            const isSamePropertyType = newRoomsOnly.every(item => String(item.propertyType) === String(propertyType));
            if (!isSamePropertyType) {
                Message.warning('该房源和已选择房源物业类型不同，请重新选择');
                return;
            }
        }
        
        // 校验通过，添加新房源
        roomList.push(...newRoomsToAdd);
        
        // 更新房源列表 - 只包含新房源
        tableData.value = roomList;
        tableQueryData.value = cloneDeep(tableData.value);
        // 将所有新房源默认勾选
        selectedKeys.value = tableData.value.map(item => item.roomId);
        
        console.log('handleCheck - 更新后的房源列表:', tableData.value);
        console.log('handleCheck - 更新后的tableQueryData:', tableQueryData.value);
        console.log('handleCheck - 选中的房源ID:', selectedKeys.value);
        
        // 强制触发响应式更新
        nextTick(() => {
            console.log('handleCheck - nextTick后的tableQueryData:', tableQueryData.value);
            console.log('handleCheck - tableQueryData长度:', tableQueryData.value.length);
        });
    } else {
        console.log('handleCheck - 没有新的房源需要添加');
    }
}

// 选中房源列表 - 只显示新房源
const tableData = ref<ContractRoomDTO[]>([]);
const selectedKeys = ref<string[]>([]);
const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
});
const tableQuery = ref('');
const tableQueryData = ref<ContractRoomDTO[]>([]);

// 表格列定义
const tableColumns = computed(() => [
    {
        title: contractData.value.contractType === 2 ? '多经点位' : '房源',
        dataIndex: 'roomName',
        key: 'roomName',
        render: ({ record }: { record: any }) => {
            return displayName(record);
        }
    }
]);
const handleTableQuery = () => {
    console.log('handleTableQuery - 搜索关键词:', tableQuery.value);
    console.log('handleTableQuery - 原始tableData:', tableData.value);
    
    // 搜索时显示新房源和已经选择的房源
    tableQueryData.value = tableData.value.filter((item: ContractRoomDTO) => {
        // 如果是已经选择的新房源，保留
        if (item.type === 1) {
            return item.roomName.includes(tableQuery.value);
        }
        // 否则过滤掉原有合同的房源
        if (originalContractRoomIds.value.includes(item.roomId)) {
            return false;
        }
        return item.roomName.includes(tableQuery.value);
    });
    
    console.log('handleTableQuery - 过滤后的tableQueryData:', tableQueryData.value);
}
const handleRemove = () => {
    // 移除选中的房源
    tableData.value = tableData.value.filter((item: ContractRoomDTO) => {
        return !selectedKeys.value.some(key => key === item.roomId);
    });
    tableQueryData.value = cloneDeep(tableData.value);
    // 更新选中状态，包含新房源和已经选择的房源
    checkedKeys.value = tableData.value
        .filter(item => item.type === 1 || !originalContractRoomIds.value.includes(item.roomId))
        .map(item => item.roomId);
}

// 表格选择变化处理
const handleSelectionChange = (selectedRowKeys: string[]) => {
    console.log('handleSelectionChange - 选中的房源ID:', selectedRowKeys);
    
    // 根据选中的房源ID从tableQueryData中获取对应的房源数据（表格实际显示的数据）
    const selectedNewRooms = tableQueryData.value.filter(item => 
        selectedRowKeys.includes(item.roomId) && 
        (item.type === 1 || !originalContractRoomIds.value.includes(item.roomId))
    );
    
    console.log('handleSelectionChange - 选中的新房源:', selectedNewRooms);
    
    if (selectedNewRooms.length > 1) {
        // 判断选择的房源计价方式和物业类型是否一样
        const priceUnit = selectedNewRooms[0].priceUnit;
        const isSamePriceUnit = selectedNewRooms.every(item => String(item.priceUnit) === String(priceUnit));
        if (!isSamePriceUnit) {
            Message.warning('该房源和已选择房源计价方式不同，请重新选择');
            // 恢复之前的选择状态，移除最后添加的房源
            const lastAddedKey = selectedRowKeys.find(key => !selectedKeys.value.includes(key));
            if (lastAddedKey) {
                selectedKeys.value = selectedKeys.value.filter(key => key !== lastAddedKey);
            }
            return;
        }
        
        const propertyType = selectedNewRooms[0].propertyType;
        const isSamePropertyType = selectedNewRooms.every(item => String(item.propertyType) === String(propertyType));
        if (!isSamePropertyType) {
            Message.warning('该房源和已选择房源物业类型不同，请重新选择');
            // 恢复之前的选择状态，移除最后添加的房源
            const lastAddedKey = selectedRowKeys.find(key => !selectedKeys.value.includes(key));
            if (lastAddedKey) {
                selectedKeys.value = selectedKeys.value.filter(key => key !== lastAddedKey);
            }
            return;
        }
    }
    
    // 校验通过，更新选中状态
    selectedKeys.value = selectedRowKeys;
}

const emit = defineEmits(['submit']);
/**
 * 选择的房间需要是相同立项定价-计价方式、相同立项定价-物业类型；否则提示：您选择的房源与已选择房源的计价方式/物业类型不同，请重新选择
 */
const handleOk = () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请选择房源')
        return
    }
    // 获取选中的房源，包括新房源和已经选择的房源
    const list = tableData.value.filter(item => selectedKeys.value.includes(item.roomId))
    
    // 过滤出新房源（type === 1）和不在原有合同中的房源
    const newRoomsOnly = list.filter(item => item.type === 1 || !originalContractRoomIds.value.includes(item.roomId));
    
    if (newRoomsOnly.length === 0) {
        Message.warning('请选择新房源')
        return
    }
    
    // 判断选择的房源计价方式和物业类型是否一样
    const priceUnit = newRoomsOnly[0].priceUnit
    const isSamePriceUnit = newRoomsOnly.every(item => String(item.priceUnit) === String(priceUnit))
    if (!isSamePriceUnit) {
        Message.warning('您选择的房源与已选择房源的计价方式不同，请重新选择')
        return
    }
    const propertyType = newRoomsOnly[0].propertyType
    const isSamePropertyType = newRoomsOnly.every(item => String(item.propertyType) === String(propertyType))
    if (!isSamePropertyType) {
        Message.warning('您选择的房源与已选择房源的物业类型不同，请重新选择')
        return
    }
    const rooms = newRoomsOnly.map(item => {
        return {
            ...item,
            discount: 100
        }
    })
    emit('submit', rooms);
    visible.value = false;
}
const handleCancel = () => {
    visible.value = false;
}

const displayName = (record:any) => {
    // 确保类型匹配，将propertyType转换为字符串进行比较
    const propertyTypeStr = String(record.propertyType)
    const propertyTypeItem = property_type.value.find((item: any) => String(item.value) === propertyTypeStr)
    const propertyTypeLabel = propertyTypeItem ? propertyTypeItem.label : ''
    return record.roomName + (propertyTypeLabel ? ' ' + propertyTypeLabel : '')
}

defineExpose({
    openDrawer
})
</script>

<style lang="less" scoped>
.table-query {
    position: relative;
    width: 100%;
    .remove-btn {
        position: absolute;
        right: 0;
        top: 0;
    }
}
</style>