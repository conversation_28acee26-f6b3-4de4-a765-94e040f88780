<template>
  <a-drawer
    v-model:visible="visible"
    title="查看收据"
    class="common-drawer-small"
    @cancel="handleCancel"
    :footer="false"
  >
    <div class="receipt-drawer-content">
      <!-- 表格区域 -->
      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="columns"
        :data="tableData"
        :bordered="{ cell: true }"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>

        <template #serialNumber="{ record }">
          <span>{{ record.serialNumber || '-' }}</span>
        </template>
        
        <template #operations="{ record }">
          <a-space>
            <a-button type="text" size="mini" @click="handleView(record)">
              查看
            </a-button>
            <a-button type="text" size="mini" @click="handleDownload(record)">
              下载
            </a-button>
          </a-space>
        </template>
      </a-table>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getReceiptList, getReceiptDetail, type ReceiptQueryDTO, type ReceiptDetailDTO } from '@/api/receipt'

// Props
interface Props {
  costId?: string // 账单ID
}

const props = withDefaults(defineProps<Props>(), {
  costId: ''
})

// Emits
const emit = defineEmits<{
  cancel: []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const tableData = ref<any[]>([])

const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
})

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    slotName: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '票据号',
    dataIndex: 'serialNumber',
    slotName: 'serialNumber',
    width: 200,
    align: 'center',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 120,
    align: 'center',
    fixed: 'right'
  }
]

// 方法
const open = () => {
  visible.value = true
  if (props.costId) {
    fetchData()
  }
}

const handleCancel = () => {
  visible.value = false
  emit('cancel')
}

// 查看收据
const handleView = async (record: any) => {
  if (!record.id) {
    Message.warning('缺少收据ID')
    return
  }

  try {
    const params: ReceiptDetailDTO = { id: record.id }
    const response = await getReceiptDetail(params)

    if (response && response.data) {
      const detail = response.data
      // 详情接口返回的数据中包含 viewUrl 字段
      if (detail.viewUrl) {
        window.open(detail.viewUrl, '_blank')
      } else {
        Message.warning('暂无查看链接')
      }
    } else {
      Message.warning('获取收据详情失败')
    }
  } catch (error) {
    console.error('获取收据详情失败:', error)
  }
}

// 下载收据
const handleDownload = async (record: any) => {
  if (!record.id) {
    Message.warning('缺少收据ID')
    return
  }

  try {
    const params: ReceiptDetailDTO = { id: record.id }
    const response = await getReceiptDetail(params)

    if (response && response.data) {
      const detail = response.data
      // 详情接口返回的数据中包含 downloadUrl 字段
      if (detail.downloadUrl) {
        // 创建a标签下载
        const link = document.createElement('a')
        link.href = detail.downloadUrl
        link.target = '_blank'
        link.rel = 'noopener noreferrer'
        // 尝试设置下载属性
        link.setAttribute('download', `收据_${record.serialNumber || record.id}.pdf`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        Message.success('下载成功')
      } else {
        Message.warning('暂无下载链接')
      }
    } else {
      Message.warning('获取收据详情失败')
    }
  } catch (error) {
    console.error('获取收据详情失败:', error)
  }
}

// 分页处理
const onPageChange = (current: number) => {
  pagination.current = current
  fetchData()
}

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchData()
}

// 获取收据列表数据
const fetchData = async () => {
  if (!props.costId) {
    Message.warning('缺少账单ID参数')
    return
  }

  loading.value = true
  try {
    const params: ReceiptQueryDTO = {
      costId: props.costId
    }

    const response: any = await getReceiptList(params)
    // 根据新的接口格式处理数据 - 接口返回的是 { code: 200, data: [...] } 格式
    if (response && response.code === 200 && response.data) {
      tableData.value = Array.isArray(response.data) ? response.data : []
      pagination.total = Array.isArray(response.data) ? response.data.length : 0
    } else {
      // 兼容原有格式 { rows: [...], total: number }
      tableData.value = response.rows || []
      pagination.total = response.total || 0
    }
  } catch (error) {
    console.error('获取收据列表失败:', error)
    Message.error('获取收据列表失败')
  } finally {
    loading.value = false
  }
}

// 监听costId变化
watch(() => props.costId, (newCostId) => {
  if (newCostId && visible.value) {
    pagination.current = 1
    fetchData()
  }
})

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped lang="less">
.receipt-drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
