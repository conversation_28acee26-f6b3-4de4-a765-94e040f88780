<template>
        <div class="refund-detail-container">
            <!-- 退款申请信息 -->
            <div class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>退款申请信息</span>
                    <!-- <a-button v-if="!isEditMode && canEdit" type="text" @click="handleEdit">编辑</a-button> -->
                </div>

                <!-- 查看模式 -->
                <div v-if="!isEditMode" class="view-info">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">退款类型：</span>
                            <a-tag :color="getRefundTypeColor(refundData.refundType)">
                                {{ getRefundTypeName(refundData.refundType) }}
                            </a-tag>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退款申请日期：</span>
                            <span class="info-value">{{ refundData.applyTime }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退款费用类型：</span>
                            <a-tag color="blue">{{ refundData.feeType }}</a-tag>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">退款金额：</span>
                            <span class="info-value money">{{ formatMoney(refundData.refundAmount) }}元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退款方式：</span>
                            <a-tag :color="getRefundMethodColor(refundData.refundWay)">
                                {{ getRefundMethodName(refundData.refundWay) }}
                            </a-tag>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退款状态：</span>
                            <a-tag :color="getRefundStatusColor(refundData.refundStatus)">
                                {{ getRefundStatusName(refundData.refundStatus) }}
                            </a-tag>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">收款方姓名：</span>
                            <span class="info-value">{{ refundData.receiverName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">收款方开户行：</span>
                            <span class="info-value">{{ refundData.receiverBank }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">收款方银行账号：</span>
                            <span class="info-value">{{ refundData.receiverAccount }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item full-width">
                            <span class="info-label">退款申请说明：</span>
                            <div class="info-value remark-content">{{ refundData.refundRemark || '暂无说明' }}</div>
                        </div>
                    </div>
                </div>

                <!-- 编辑模式 -->
                <a-form v-else ref="formRef" :model="formData" :label-col-props="{ span: 9 }"
                    :wrapper-col-props="{ span: 15 }" label-align="right">
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item field="refundType" label="退款类型"
                                :rules="[{ required: true, message: '请选择退款类型' }]">
                                <a-select v-model="formData.refundType" placeholder="请选择退款类型" :disabled="true">
                                    <a-option :value="0">退租退款</a-option>
                                    <a-option :value="1">退定退款</a-option>
                                    <a-option :value="2">未明流水退款</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="applyTime" label="退款申请日期"
                                :rules="[{ required: true, message: '请选择退款申请日期' }]">
                                <a-date-picker v-model="formData.applyTime" style="width: 100%" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="feeType" label="退款费用类型"
                                :rules="[{ required: true, message: '请选择退款费用类型' }]">
                                <a-select v-model="formData.feeType" placeholder="请选择退款费用类型" disabled>
                                    <a-option value="保证金">保证金</a-option>
                                    <a-option value="租金">租金</a-option>
                                    <a-option value="定金">定金</a-option>
                                    <a-option value="未明流水">未明流水</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="refundAmount" label="退款金额"
                                :rules="[{ required: true, message: '请输入退款金额' }]">
                                <a-input-number v-model="formData.refundAmount" placeholder="请输入退款金额"
                                    style="width: 100%" :precision="2" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="refundWay" label="退款方式"
                                :rules="[{ required: true, message: '请选择退款方式' }]">
                                <a-select v-model="formData.refundWay" placeholder="请选择退款方式">
                                    <a-option :value="0">原路退回</a-option>
                                    <a-option :value="1">银行转账</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverName" label="收款方姓名"
                                :rules="[{ required: true, message: '请输入收款方姓名' }]">
                                <a-input v-model="formData.receiverName" placeholder="请输入收款方姓名" allow-clear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverBank" label="收款方开户行">
                                <a-input v-model="formData.receiverBank" placeholder="请输入收款方开户行" allow-clear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="receiverAccount" label="收款方银行账号"
                                :rules="[{ required: true, message: '请输入收款方银行账号' }]">
                                <a-input v-model="formData.receiverAccount" placeholder="请输入收款方银行账号" allow-clear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24" :offset="0">
                            <a-form-item field="refundRemark" label="退款申请说明" :label-col-props="{ span: 3 }"
                                :wrapper-col-props="{ span: 21 }">
                                <a-textarea v-model="formData.refundRemark" placeholder="请输入退款申请说明" allow-clear
                                    :max-length="200" show-word-limit />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>

            <!-- 根据退款类型显示不同内容 -->
            <!-- 退租退款 - 显示退租信息 -->
            <div v-if="refundData.refundType === 0" class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>退租信息</span>
                    <!-- <a-button type="text" @click="handleViewContractDetail">查看详情</a-button> -->
                </div>
                <div class="base-info">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">承租方：</span>
                            <span class="info-value">{{ contractInfo.createByName || '暂无' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退租类型：</span>
                            <span class="info-value">{{ contractInfo.terminateType === 0 ? '到期退租' : '提前退租' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">退租日期：</span>
                            <span class="info-value">{{ contractInfo.terminateDate }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">合同用途：</span>
                            <span class="info-value">{{ contractInfo.terminateRemark || '暂无' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ contractInfo.contractId }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">合同终止日期：</span>
                            <span class="info-value">{{ contractInfo.terminateDate }}</span>
                        </div>
                    </div>
                </div>
            </div>


            <!-- 退定退款 - 显示定单信息 -->
            <div v-if="refundData.refundType === 1" class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>定单信息</span>
                    <!-- <a-button type="text" @click="handleViewOrderDetail">查看详情</a-button> -->
                </div>
                <div class="base-info">
                    <div class="info-row">
                        <!-- <div class="info-item">
                            <span class="info-label">订单编号：</span>
                            <span class="info-value">{{ orderInfo.bookingNo }}</span>
                        </div> -->
                        <div class="info-item">
                            <span class="info-label">客户名称：</span>
                            <span class="info-value">{{ orderInfo.customerName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">意向房源：</span>
                            <span class="info-value">{{ orderInfo.roomName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">定金应收金额：</span>
                            <span class="info-value money">{{ formatMoney(orderInfo.bookingAmount) }}元</span>
                        </div>


                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">定金已收金额：</span>
                            <span class="info-value money">{{ formatMoney(orderInfo.receivedAmount) }}元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">收款方式：</span>
                            <span class="info-value">{{ getPayMethodName(Number(orderInfo.payMethod)) }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">缴纳日期：</span>
                            <span class="info-value">{{ orderInfo.receivedDate || '暂无' }}</span>
                        </div>

                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">是否退定金：</span>
                            <span class="info-value">{{ orderInfo.isRefund ? '是' : '否' }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">退定说明：</span>
                            <span class="info-value">{{ orderInfo.cancelRemark || '暂无' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 未明流水退款 - 显示流水信息 -->
            <div v-if="refundData.refundType === 2" class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>流水信息</span>
                    <a-button type="text" @click="handleViewFlowDetail">查看详情</a-button>
                </div>
                <div class="base-info">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">流水编号：</span>
                            <span class="info-value">{{ flowInfo.orderNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付金额：</span>
                            <span class="info-value money">{{ formatMoney(flowInfo.amount) }}元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付时间：</span>
                            <span class="info-value">{{ flowInfo.entryTime }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">支付方式：</span>
                            <span class="info-value">{{ getPayMethodName(flowInfo.payMethod) }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付人：</span>
                            <span class="info-value">{{ flowInfo.payerName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">流水状态：</span>
                            <span class="info-value">{{ getFlowStatusName(flowInfo.status) }}</span>
                        </div>
                    </div>
                </div>
            </div>



            <!-- 退款结算明细信息 - 退租退款专用 -->
            <div v-if="refundData.refundType === 0" class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>退款结算明细</span>
                </div>
                <a-table :data="settlementDetailData" :columns="settlementDetailColumns" :pagination="false"
                    :bordered="{ cell: true }" :summary="true">
                    <template #settlementAmount="{ record }">
                        <span class="money" :class="{ 'negative': record.amount < 0 }">
                            {{ record.amount >= 0 ? '+' : '' }}{{ formatMoney(record.amount) }}元
                        </span>
                    </template>
                    <template #summary-cell="{ column, record }">
                        <template v-if="column.dataIndex === 'feeCategory'">
                            <span style="font-weight: bold;">合计</span>
                        </template>
                        <template v-else-if="column.dataIndex === 'amount'">
                            <span class="money total-amount" style="font-weight: bold;">
                                {{formatMoney(settlementDetailData.reduce((sum, item) => sum + item.amount, 0))}}元
                            </span>
                        </template>
                    </template>
                </a-table>
            </div>

            <!-- 退租结算信息 -->
            <div v-if="refundData.refundType === 0" class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>退租结算信息</span>
                </div>
                <div class="base-info">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">减免金额：</span>
                            <span class="info-value">{{ formatMoney(settlementInfo.discountAmount) }}元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">最终费用金额：</span>
                            <span class="info-value">{{ formatMoney(settlementInfo.finalAmount) }}元</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">减免原因：</span>
                            <span class="info-value">{{ settlementInfo.discountReason }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">营业执照：</span>
                            <span class="info-value">{{ settlementInfo.businessLicense }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">税务登记证：</span>
                            <span class="info-value">{{ settlementInfo.taxRegistration }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 退款流水信息 - 查看详情时显示 -->
            <div v-if="!isEditMode" class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>退款流水信息</span>
                </div>
                <a-table :data="refundFlowData" :columns="refundFlowColumns" :pagination="false"
                    :bordered="{ cell: true }">
                    <template #flowStatus="{ record }">
                        <a-tag :color="getFlowStatusColor(record.flowStatus)">
                            {{ record.flowStatus }}
                        </a-tag>
                    </template>
                    <template #amount="{ record }">
                        <span class="money">{{ formatMoney(record.amount) }}元</span>
                    </template>
                </a-table>
            </div>

            <!-- 附件信息 -->
            <div class="form-section">
                <div class="section-title">
                    <div class="section-marker"></div>
                    <span>附件信息</span>
                </div>
                <div v-if="!isEditMode && attachmentList.length > 0" class="attachment-list">
                    <div v-for="(file, index) in attachmentList" :key="index" class="attachment-item">
                        <icon-file />
                        <span class="file-name">{{ file.name }}</span>
                        <a-button type="text" size="mini" @click="handleDownloadFile(file)">下载</a-button>
                    </div>
                </div>
                <div v-else-if="!isEditMode && attachmentList.length === 0" class="no-attachment">
                    暂无附件
                </div>
                <div v-if="isEditMode" class="attachment-upload">
                    <a-upload action="/" :file-list="fileList" @change="handleFileChange"
                        :custom-request="customUploadRequest" multiple>
                        <template #upload-button>
                            <a-button>上传附件</a-button>
                        </template>
                    </a-upload>
                </div>
            </div>
        </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import type { TableColumnData } from '@arco-design/web-vue'
import { FormInstance } from '@arco-design/web-vue'
import {
    getFinancialRefundDetail,
    saveFinancialRefund,
    type FinancialRefundVo,
    type FinancialRefundAddDTO,
    type FinancialRefundDetailVo,
    type ContractTerminateVo,
    type BookingVo,
    type FinancialFlowVo,
    type CostFlowRelVo
} from '@/api/refundManage'

const props = defineProps<{
    refundData: FinancialRefundVo
    isEditMode: boolean
}>()

// 抽屉控制
const drawerVisible = ref(false)
const isEditMode = ref(false)
const formRef = ref<FormInstance>()

// 计算标题
const drawerTitle = computed(() => {
    return isEditMode.value ? '编辑退款详情' : '查看退款详情'
})

// 退款数据
const refundData = reactive<FinancialRefundVo>({
    id: '',
    projectName: '',
    refundType: 0,
    bizId: '',
    refundTarget: '',
    applyTime: '',
    refundAmount: 0,
    feeType: '',
    refundWay: 0,
    receiverName: '',
    receiverBank: '',
    receiverAccount: '',
    refundRemark: '',
    refundTime: '',
    refundStatus: 0,
    approveStatus: 0,
    attachments: ''
})

// 表单数据
const formData = reactive<FinancialRefundAddDTO>({
    id: '',
    projectId: '',
    refundType: 0,
    bizId: '',
    refundTarget: '',
    applyTime: '',
    refundAmount: 0,
    feeType: '',
    refundWay: 0,
    receiverName: '',
    receiverBank: '',
    receiverAccount: '',
    refundRemark: '',
    attachments: ''
})

// 判断是否可编辑（仅草稿状态可编辑）
const canEdit = computed(() => {
    return refundData.refundStatus === 0
})

// 合同信息（退租退款时显示）
const contractInfo = reactive<ContractTerminateVo>({
    id: '',
    contractId: '',
    unionId: '',
    bondReceivedAmount: 0,
    rentReceivedAmount: 0,
    rentOverdueAmount: 0,
    receivedPeriod: '',
    overduePeriod: '',
    terminateType: 0,
    terminateDate: '',
    terminateReason: '',
    otherReasonDesc: '',
    hasOtherDeduction: false,
    otherDeductionDesc: '',
    terminateRemark: '',
    terminateAttachments: '',
    createByName: '',
    updateByName: '',
    isDel: false
})

// 订单信息（退定退款时显示）
const orderInfo = reactive<BookingVo>({
    id: '',
    projectId: '',
    projectName: '',
    customerName: '',
    propertyType: '',
    roomId: '',
    roomName: '',
    bookingNo: '',
    bookingAmount: 0,
    receivableDate: '',
    expectSignDate: '',
    isRefundable: 0,
    cancelTime: '',
    cancelBy: '',
    cancelByName: '',
    cancelReason: 0,
    isRefund: 0,
    cancelEnclosure: '',
    cancelRemark: '',
    status: 0,
    contractId: '',
    refundId: '',
    createBy: '',
    createByName: '',
    createTime: '',
    updateBy: '',
    updateByName: '',
    updateTime: '',
    isDel: false,
    contractNo: '',
    signDate: '',
    contractLeaseUnit: '',
    lesseeName: '',
    receivedAmount: 0,
    receivedDate: '',
    payMethod: ''
})

// 流水信息（未明流水退款时显示）
const flowInfo = reactive<FinancialFlowVo>({
    id: '',
    projectId: '',
    orderNo: '',
    payDirection: 0,
    payType: 0,
    payMethod: 0,
    targetType: 0,
    target: '',
    entryTime: '',
    status: 0,
    amount: 0,
    usedAmount: 0,
    payerName: '',
    payerPhone: '',
    payerAccount: '',
    payRemark: '',
    merchant: '',
    payChannel: '',
    sourceNo: '',
    isOtherIncome: false,
    otherIncomeDesc: '',
    createByName: '',
    updateByName: '',
    isDel: false
})

// 退款流水数据
const refundFlowData = ref<Array<{
    flowId: string
    operationType: string
    amount: number
    operationTime: string
    operatorName: string
    flowStatus: string
    remark: string
}>>([])

// 退款结算明细数据（退租退款专用）
const settlementDetailData = ref<Array<{
    incomeType: string
    feeCategory: string
    amount: number
    feePeriod: string
    feeDescription: string
}>>([])

// 退款结算明细表格列定义
const settlementDetailColumns = ref<TableColumnData[]>([
    {
        title: '收支类型',
        dataIndex: 'incomeType',
        width: 120,
        align: 'center'
    },
    {
        title: '费用科目',
        dataIndex: 'feeCategory',
        width: 150,
        align: 'center'
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 120,
        align: 'center',
        slotName: 'settlementAmount'
    },
    {
        title: '费用周期',
        dataIndex: 'feePeriod',
        width: 180,
        align: 'center'
    },
    {
        title: '费用说明',
        dataIndex: 'feeDescription',
        align: 'center',
        ellipsis: true,
        tooltip: true
    }
])

// 退租结算信息
const settlementInfo = reactive({
    discountAmount: 0,        // 减免金额
    finalAmount: 0,           // 最终费用金额
    discountReason: '',       // 减免原因
    businessLicense: '',      // 营业执照
    taxRegistration: ''       // 税务登记证
})

// 附件列表
const attachmentList = ref<any[]>([])
const fileList = ref<any[]>([])

// 退款类型名称映射
const getRefundTypeName = (type: number | undefined) => {
    const typeMap: Record<number, string> = {
        0: '退租退款',
        1: '退定退款',
        2: '未明流水退款'
    }
    return typeMap[type || 0] || ''
}

// 退款状态名称映射
const getRefundStatusName = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '草稿',
        1: '待退款',
        2: '已退款',
        3: '作废'
    }
    return statusMap[status || 0] || ''
}

// 退款方式名称映射
const getRefundMethodName = (method: number | undefined) => {
    const methodMap: Record<number, string> = {
        0: '原路退回',
        1: '银行转账'
    }
    return methodMap[method || 0] || ''
}

// 获取退款类型颜色
const getRefundTypeColor = (type: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'purple',    // 退租退款
        1: 'blue',      // 退定退款
        2: 'cyan'       // 未明流水退款
    }
    return colorMap[type || 0] || 'default'
}

// 获取退款状态颜色
const getRefundStatusColor = (status: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'gray',      // 草稿
        1: 'orange',    // 待退款
        2: 'green',     // 已退款
        3: 'red'        // 作废
    }
    return colorMap[status || 0] || 'default'
}

// 获取退款方式颜色
const getRefundMethodColor = (method: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'green',     // 原路退回
        1: 'orange'     // 银行转账
    }
    return colorMap[method || 0] || 'default'
}

// 获取流水状态颜色
const getFlowStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        '已完成': 'green',
        '处理中': 'orange',
        '已取消': 'red'
    }
    return colorMap[status] || 'default'
}

// 金额格式化
const formatMoney = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return '0.00'
    return amount.toFixed(2)
}

// 获取订单状态名称
const getOrderStatusName = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '草稿',
        1: '待收费',
        2: '已生效',
        3: '已转签',
        4: '已作废'
    }
    return statusMap[status || 0] || ''
}

// 获取支付方式名称
const getPayMethodName = (method: number | undefined) => {
    const methodMap: Record<number, string> = {
        0: '微信支付',
        1: '支付宝',
        2: '银行转账',
        3: '现金'
    }
    return methodMap[method || 0] || ''
}

// 获取流水状态名称
const getFlowStatusName = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '未记账',
        1: '部分记账',
        2: '已记账'
    }
    return statusMap[status || 0] || ''
}

// 加载退款详情数据
const loadRefundDetail = async (refundId: string, refundType?: number, bizId?: string, mode?: string) => {
    try {
        const response = await getFinancialRefundDetail(refundId, refundType, bizId)
        if (response.code === 200 && response.data) {
            const detailData = response.data

            // 更新退款基本信息
            if (detailData.refund) {
                Object.assign(refundData, detailData.refund)
            }

            // 解析附件
            if (refundData.attachments) {
                try {
                    attachmentList.value = JSON.parse(refundData.attachments)
                } catch (e) {
                    attachmentList.value = []
                }
            }

            console.log('refundData', refundData)
            console.log('refundRemark值:', refundData.refundRemark)

            // 根据退款类型加载相关信息
            if (refundData.refundType === 0 && detailData.terminate) {
                // 退租退款 - 加载合同终止信息
                Object.assign(contractInfo, detailData.terminate)

                // 加载退款结算明细数据（模拟数据，实际应从API获取）
                settlementDetailData.value = [
                    {
                        incomeType: '收入',
                        feeCategory: '保证金',
                        amount: 5000,
                        feePeriod: '2024-01-01 至 2024-12-31',
                        feeDescription: '房屋保证金退还'
                    },
                    {
                        incomeType: '支出',
                        feeCategory: '水电费',
                        amount: -200,
                        feePeriod: '2024-12-01 至 2024-12-31',
                        feeDescription: '水电费扣除'
                    },
                    {
                        incomeType: '支出',
                        feeCategory: '维修费',
                        amount: -300,
                        feePeriod: '2024-12-15',
                        feeDescription: '房屋维修费用'
                    }
                ]

                // 加载退租结算信息（模拟数据，实际应从API获取）
                Object.assign(settlementInfo, {
                    discountAmount: 100,
                    finalAmount: 4500,
                    discountReason: '首次租户优惠',
                    businessLicense: '已提供',
                    taxRegistration: '已提供'
                })
            } else if (refundData.refundType === 1 && detailData.booking) {
                // 退定退款 - 加载订单信息
                Object.assign(orderInfo, detailData.booking)
            } else if (refundData.refundType === 2 && detailData.flow) {
                // 未明流水退款 - 加载流水信息
                Object.assign(flowInfo, detailData.flow)
            }

            // 加载退款流水信息
            if (detailData.flowRels && detailData.flowRels.length > 0) {
                refundFlowData.value = detailData.flowRels.map(item => ({
                    flowId: item.flowNo || '',
                    operationType: getOperationTypeName(item.type),
                    amount: item.payAmount || 0,
                    operationTime: item.createTime || '',
                    operatorName: item.createByName || '',
                    flowStatus: getConfirmStatusName(item.confirmStatus),
                    remark: item.target || ''
                }))
            }
            if (mode === 'edit') {
                handleEdit()
            }
        }
    } catch (error) {
        console.error('加载退款详情失败:', error)
        Message.error('加载退款详情失败')
    }
}

// 获取操作类型名称
const getOperationTypeName = (type: number | undefined) => {
    const typeMap: Record<number, string> = {
        1: '收款',
        2: '转入',
        3: '转出',
        4: '退款'
    }
    return typeMap[type || 0] || ''
}

// 获取确认状态名称
const getConfirmStatusName = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '未确认',
        1: '自动确认',
        2: '手动确认'
    }
    return statusMap[status || 0] || ''
}

// 切换到编辑模式
const handleEdit = () => {
    isEditMode.value = true
    // 复制数据到表单
    Object.assign(formData, {
        id: refundData.id,
        projectId: refundData.projectId,
        refundType: refundData.refundType,
        bizId: refundData.bizId,
        refundTarget: refundData.refundTarget,
        applyTime: refundData.applyTime,
        refundAmount: refundData.refundAmount,
        feeType: refundData.feeType,
        refundWay: refundData.refundWay,
        receiverName: refundData.receiverName,
        receiverBank: refundData.receiverBank,
        receiverAccount: refundData.receiverAccount,
        refundRemark: refundData.refundRemark,
        attachments: refundData.attachments
    })

    console.log('----formData.refundRemark值:', formData)

    // 复制附件到文件列表
    fileList.value = [...attachmentList.value]
}

// 取消编辑
const handleCancelEdit = () => {
    isEditMode.value = false
    drawerVisible.value = false
    fileList.value = []
}

// 保存
const handleSave = async () => {
    try {
        const errors = await formRef.value?.validate()
        if (errors) return

        // 构建保存数据
        const saveData: FinancialRefundAddDTO = {
            ...formData,
            refundType: typeof formData.refundType === 'string' ? Number(formData.refundType) : formData.refundType,
            refundWay: typeof formData.refundWay === 'string' ? Number(formData.refundWay) : formData.refundWay,
            attachments: JSON.stringify(fileList.value),
            isSubmit: false // 暂存
        }

        const response = await saveFinancialRefund(saveData)
        if (response.code === 200) {
            Message.success('保存成功')
            isEditMode.value = false
            drawerVisible.value = false
            // 重新加载数据
            if (refundData.id) {
                loadRefundDetail(refundData.id, refundData.refundType, refundData.bizId, 'view')
            }
        } else {
            Message.error(response.msg || '保存失败')
        }
    } catch (error) {
        console.error('保存失败:', error)
    }
}

// 提交审批
const handleSubmit = async () => {
    try {
        const errors = await formRef.value?.validate()
        if (errors) return

        // 构建提交数据
        const submitData: FinancialRefundAddDTO = {
            ...formData,
            refundType: typeof formData.refundType === 'string' ? Number(formData.refundType) : formData.refundType,
            refundWay: typeof formData.refundWay === 'string' ? Number(formData.refundWay) : formData.refundWay,
            attachments: JSON.stringify(fileList.value),
            isSubmit: true // 提交审批
        }

        const response = await saveFinancialRefund(submitData)
        if (response.code === 200) {
            Message.success('提交成功')
            isEditMode.value = false
            drawerVisible.value = false
            // 通知父组件刷新数据
            emit('refresh')
        } else {
            Message.error(response.msg || '提交失败')
        }
    } catch (error) {
        console.error('提交失败:', error)
    }
}

// 取消
const handleCancel = () => {
    drawerVisible.value = false
    isEditMode.value = false
    fileList.value = []
}

// 查看合同详情
const handleViewContractDetail = () => {
    console.log('查看合同详情:', contractInfo.contractId)
    // TODO: 实现查看合同详情功能
}

// 查看订单详情
const handleViewOrderDetail = () => {
    console.log('查看订单详情:', orderInfo.bookingNo)
    // TODO: 实现查看订单详情功能
}

// 查看流水详情
const handleViewFlowDetail = () => {
    console.log('查看流水详情:', flowInfo.orderNo)
    // TODO: 实现查看流水详情功能
}

// 下载附件
const handleDownloadFile = (file: any) => {
    console.log('下载文件:', file)
    // TODO: 实现文件下载功能
}

// 文件上传处理
const handleFileChange = (fileList: any[]) => {
    console.log('文件变更:', fileList)
}

// 自定义上传请求
const customUploadRequest = (options: any) => {
    const { onProgress, onSuccess, onError, file } = options

    // 模拟上传过程
    const timer = setInterval(() => {
        const percent = Math.floor(Math.random() * 10) + 10
        onProgress(percent)
    }, 300)

    // 模拟上传完成
    setTimeout(() => {
        clearInterval(timer)
        onProgress(100)
        onSuccess()
    }, 1000)
}

// 定义事件
const emit = defineEmits<{
    refresh: []
}>()

// 暴露方法给父组件
defineExpose({
    open(refundId: string, mode: 'view' | 'edit' = 'view', record: FinancialRefundVo) {
        // 先设置基础数据
        Object.assign(refundData, record)
        refundData.id = refundId
        console.log('直接设置的refundData:', refundData)
        console.log('直接设置的refundRemark:', refundData.refundRemark)

        // 如果有ID，再通过API加载详细数据
        if (!!refundId) {
            loadRefundDetail(refundId, record.refundType, record.bizId, mode)
        }

        isEditMode.value = mode === 'edit'
        drawerVisible.value = true
    },
    getFormData() {
        return formData
    }
    
    // 直接通过 refundId 调用接口的方法
    // async openByRefundId(refundId: string, mode: 'view' | 'edit' = 'view') {
    //     console.log('通过 refundId 打开:', refundId, mode)
        
    //     // 重置数据
    //     Object.assign(refundData, {
    //         id: '',
    //         projectName: '',
    //         refundType: 0,
    //         bizId: '',
    //         refundTarget: '',
    //         applyTime: '',
    //         refundAmount: 0,
    //         feeType: '',
    //         refundWay: 0,
    //         receiverName: '',
    //         receiverBank: '',
    //         receiverAccount: '',
    //         refundRemark: '',
    //         refundTime: '',
    //         refundStatus: 0,
    //         approveStatus: 0,
    //         attachments: ''
    //     })
        
    //     // 直接调用详情接口
    //     await loadRefundDetail(refundId, undefined, undefined, mode)
        
    //     isEditMode.value = mode === 'edit'
    //     drawerVisible.value = true
    // }
})

// 退款流水表格列定义
const refundFlowColumns = ref<TableColumnData[]>([
    {
        title: '流水编号',
        dataIndex: 'flowId',
        width: 180,
        align: 'center',
        ellipsis: true,
        tooltip: true
    },
    {
        title: '操作类型',
        dataIndex: 'operationType',
        width: 120,
        align: 'center'
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 120,
        align: 'center',
        slotName: 'amount'
    },
    {
        title: '操作时间',
        dataIndex: 'operationTime',
        width: 180,
        align: 'center',
        ellipsis: true, tooltip: true
    },
    {
        title: '操作人',
        dataIndex: 'operatorName',
        width: 120,
        align: 'center'
    },
    {
        title: '流水状态',
        dataIndex: 'flowStatus',
        width: 120,
        align: 'center',
        slotName: 'flowStatus'
    },
    {
        title: '备注',
        dataIndex: 'remark',
        align: 'center',
        ellipsis: true,
        tooltip: true
    }
])
</script>

<style scoped lang="less">
.refund-detail-container {
    padding: 0 16px;

    .form-section {
        margin-bottom: 24px;

        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .section-marker {
                width: 4px;
                height: 16px;
                background-color: #1890ff;
                margin-right: 8px;
                border-radius: 2px;
            }

            span {
                font-size: 16px;
                font-weight: 500;
                flex: 1;
            }
        }

        .view-info {
            .info-row {
                display: flex;
                margin-bottom: 16px;

                &:last-child {
                    margin-bottom: 0;
                }

                .info-item {
                    flex: 1;
                    display: flex;
                    align-items: center;

                    &.full-width {
                        flex: 3;
                        align-items: flex-start;
                    }

                    .info-label {
                        color: #646a73;
                        margin-right: 8px;
                        width: 120px;
                        text-align: right;
                    }

                    .info-value {
                        width: 0;
                        color: #1d2129;
                        flex: 1;

                        &.money {
                            font-weight: 600;
                            color: #f56c6c;
                        }

                        &.remark-content {
                            line-height: 1.5;
                            word-break: break-all;
                            white-space: pre-wrap;
                        }
                    }
                }
            }
        }

        .base-info {
            background-color: #f9f9f9;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 16px;

            .info-row {
                display: flex;
                margin-bottom: 12px;

                &:last-child {
                    margin-bottom: 0;
                }

                .info-item {
                    flex: 1;
                    display: flex;

                    .info-label {
                        color: #646a73;
                        margin-right: 8px;
                        // min-width: 90px;
                        width: 120px;
                        text-align: right;
                    }

                    .info-value {
                        width: 0;
                        color: #1d2129;
                        flex: 1;

                        &.money {
                            font-weight: 600;
                            color: #f56c6c;
                        }
                    }
                }
            }
        }

        .attachment-list {
            .attachment-item {
                display: flex;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                .file-name {
                    flex: 1;
                    margin-left: 8px;
                    color: #1d2129;
                }
            }
        }

        .no-attachment {
            color: #86909c;
            text-align: center;
            padding: 32px 0;
        }
    }

    .money {
        font-weight: 600;
        color: #f56c6c;

        &.negative {
            color: #52c41a;
        }

        &.total-amount {
            color: #1890ff;
            font-size: 14px;
        }
    }
}

.drawer-footer {
    display: flex;
    justify-content: flex-end;
}
</style>